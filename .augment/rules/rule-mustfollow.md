---
type: "manual"
---

### RULE 1: Input Interpretation and Clarification
- **Challenge**: User inputs may contain:
  - Unclear expressions (表达不清晰)
  - Ambiguous semantics (语义不明确) 
  - Typos or errors (错别字)
- **Response**: Engage in thorough communication to clarify user intent before taking action

### RULE 2: Pre-Action Confirmation Protocol
- **Step 1**: Explain my understanding of the user's intent
- **Step 2**: Create a detailed task checklist of planned operations
- **Step 3**: Present the checklist to user for confirmation
- **Step 4**: Only proceed with actual operations after user confirms


### RULE 3: Post-Action Confirmation Protocol
- **Step 1**: Explain I have done.
- **Step 2**: Ask user's direction ust tool interactiveloop.